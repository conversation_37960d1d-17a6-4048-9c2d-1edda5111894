package com.adins.esign.businesslogic.impl.embed;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.mail.MessagingException;
import org.springframework.transaction.annotation.Transactional;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsOtpLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.embed.TenantEmbedLogic;
import com.adins.esign.businesslogic.api.embed.UserEmbedLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.validation.SendOtpValidationBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.LivenessFaceCompareRequest;
import com.adins.esign.webservices.model.LivenessFaceCompareResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedResponse;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedResponse;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.SignConfirmationDocumentException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VidaException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.SignConfirmationDocumentException.ReasonSignConfirmationDokumen;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.util.ZipCompressionUtils;
import com.google.gson.Gson;

@Component
public class GenericUserEmbedLogic extends BaseLogic implements UserEmbedLogic{

	@Autowired private Gson gson;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private SmsOtpLogic smsOtpLogic;
	@Autowired private TenantEmbedLogic tenantEmbedLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private PrivyGeneralLogic privyGeneralLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;

	private final Set<SendOtpValidationBean> sendOtpValidationSet = ConcurrentHashMap.newKeySet();

	private static final String REGISTER_BY_INVITATION_PASSWORD = "newInv";
	private static final Logger LOG = LoggerFactory.getLogger(GenericUserEmbedLogic.class);
	private static final String VFIRST_ERR28681 = "28681";
	private static final String VFIRST_ERR28682 = "28682";	
	private static final String VFIRST_ERR408 = "408";	
	private static final String CONST_FALSE		= "False";
	private static final String CONST_SENDING_POINT_OPTION = "Sending Point Option";

	private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
	private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
	
	@Value("${verification.url}") private String urlVerification;
	@Value("${verification.liveness.facecompare.url}") private String urlLivenessFacecompare;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	@Override
	public SignerDataVerificationEmbedResponse signerDataVerificationEmbed(SignerDataVerificationEmbedRequest request,
			AuditContext audit) {
		
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(),request.getTenantCode(), true, audit);
		
		if (StringUtils.isBlank(request.getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordcannotbeemtpy", null, audit),
					ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		
		
		if (msg.getAmMsuser().getPassword().equals(REGISTER_BY_INVITATION_PASSWORD)) {
			throw new UserException(getMessage("businesslogic.user.userdoesnothaveanydocument", null, audit),
					ReasonUser.USER_DOES_NOT_HAVE_ANY_DOCUMENT);
		}
		
		if (!PasswordHash.validatePassword(request.getPassword(), msg.getAmMsuser().getPassword())) {
			throw new UserException(getMessage("businesslogic.user.passwordnotmatch", null, audit),
					ReasonUser.PASSWORD_NOT_MATCH);
		}
		
		SignerDataVerificationEmbedResponse response = new SignerDataVerificationEmbedResponse();
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
		return response;
		
	}

	@Override
	public GetSignerDetailEmbedResponse getSignerDetailEmbed(GetSignerDetailEmbedRequest request, AuditContext audit) {
		String messageValidation ="";
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		
		MsVendorRegisteredUser signerDetail = daoFactory.getUserDao().getSignerDetail(embedMsgBean.getAmMsuser().getIdMsUser(), request.getVendorCode());
		
		commonValidatorLogic.validateNotNull(request.getVendorCode(), GlobalVal.OBJECT_NAME_VENDOR_CODE, audit);
		
		commonValidatorLogic.validateNotNull(request.getTenantCode(), GlobalVal.OBJECT_NAME_TENANT_CODE, audit);
		
		messageValidation = getMessage("businesslogic.user.usernotregisteredinvendor", null, audit);
		commonValidatorLogic.validateNotNull(signerDetail, messageValidation, StatusCode.USER_NOT_REGISTERED_IN_VENDOR);
		
		GetSignerDetailEmbedResponse response = new GetSignerDetailEmbedResponse();
		
		response.setMaxLivenessFaceCompareAttempt("0");
		response.setIsNoPassword("0");
		
		if (signerDetail.getPhoneBytea() != null) {
			response.setPhoneNo(personalDataEncLogic.decryptToString(signerDetail.getPhoneBytea()));
		}
		
		AmMsuser user = embedMsgBean.getAmMsuser();
		if (user.getLivenessFacecompareValidationDate() != null && DateUtils.isSameDay(user.getLivenessFacecompareValidationDate(), new Date())) {
			AmGeneralsetting maxLivenessLimit = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT);
			if (user.getLivenessFacecompareValidationNum() != null && user.getLivenessFacecompareValidationNum() >= Short.valueOf(maxLivenessLimit.getGsValue()) ) {
				response.setMaxLivenessFaceCompareAttempt("1");
			}	
		}
		
		if (user.getLivenessFacecompareRequestDate() != null && DateUtils.isSameDay(user.getLivenessFacecompareRequestDate(), new Date())) {
			AmGeneralsetting maxLivenessLimit = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT);
			if (user.getLivenessFacecompareRequestNum() != null && user.getLivenessFacecompareRequestNum() >= Short.valueOf(maxLivenessLimit.getGsValue()) ) {
				response.setMaxLivenessFaceCompareAttempt("1");
			}	
		}
		
		if (tenantSettingsLogic.getSettingValue(embedMsgBean.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_SIGNING)) {
			response.setIsNoPassword("1");
		}
		
		response.setEmail(signerDetail.getSignerRegisteredEmail());
		return response;
	}

	@Override
	public VerifyLivenessFaceCompareEmbedResponse verifyLivenessFaceCompareEmbed(VerifyLivenessFaceCompareEmbedRequest request, AuditContext audit) {
		
		VerifyLivenessFaceCompareEmbedResponse response = new VerifyLivenessFaceCompareEmbedResponse();
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		String email = embedMsgBean.getDecryptedEmail();
		String messageValidation = "";
			
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CODE_EMPTY, null, audit);
		commonValidatorLogic.validateNotNull(request.getVendorCode(), messageValidation, StatusCode.VENDOR_CODE_EMPTY);
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, new Object[] { email }, audit);
		commonValidatorLogic.validateNotNull(user, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_EMAIL);

		
		List<String> listDocIdEncrypt = request.getDocumentId();
		if (CollectionUtils.isEmpty(listDocIdEncrypt)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM
					, new Object[] { "List Document Id" }, audit), ReasonDocument.EMPTY_DOCUMENT_ID);
		}
		TrDocumentD trDocD = new TrDocumentD();
		TrDocumentH trDocH = new TrDocumentH();
		for (String encryptedId : listDocIdEncrypt) {
			try {
				String documentId = commonLogic.decryptMessageToString(encryptedId, embedMsgBean.getMsTenant().getAesEncryptKey(), audit);
				messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID,null, audit);
				commonValidatorLogic.validateNotNull(documentId, messageValidation, StatusCode.DOCUMENT_ID_EMPTY);
				
				trDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
				messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND, new Object[] { documentId }, audit);
				commonValidatorLogic.validateNotNull(trDocD, messageValidation, StatusCode.DOCUMENT_NOT_FOUND);
				
				trDocH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(trDocD.getTrDocumentH().getRefNumber(), embedMsgBean.getMsTenant().getTenantCode());
				messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DOC_TENANT_NOT_MATCH, new Object[] { documentId,embedMsgBean.getMsTenant().getTenantCode() }, audit);
				commonValidatorLogic.validateNotNull(documentId, messageValidation, StatusCode.CANNOT_ACCESS_OTHER_TENANT_DOC);
				
	
				List<TrDocumentDSign> listTrDocDSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(trDocD.getIdDocumentD(), user.getIdMsUser());
				if (CollectionUtils.isEmpty(listTrDocDSign)) {
					throw new DocumentException(getMessage("businesslogic.document.usernotsignerofdocument",
							new String[] { user.getLoginId() }, audit), ReasonDocument.INVALID_DOCUMENT_SIGNER);
				}
				
				
			} catch (Exception e) {
				throw new EmbedMsgException(getMessage("businesslogic.embedmsg.invalidencryptedocument", null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
			}
		}
		
		AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
		PersonalDataBean pd = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		String nik = personalDataEncLogic.decryptToString(userPersonalData.getIdNoBytea());
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());
		messageValidation = getMessage("businesslogic.user.usernotregisteredinvendor", null, audit);
		commonValidatorLogic.validateNotNull(msVendorRegisteredUser, messageValidation, StatusCode.USER_NOT_REGISTERED_IN_VENDOR);
		
		
		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit),
					ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { request.getVendorCode() } , audit),
			ReasonUser.USER_CERTIFICATE_EXPIRED);
		}
		
		MsUseroftenant msUserOfTenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), request.getTenantCode());

		messageValidation = getMessage("businesslogic.user.usertenantnotfound", new String[] { email, request.getTenantCode() }, audit);
		commonValidatorLogic.validateNotNull(msUserOfTenant, messageValidation, StatusCode.USER_TENANT_NOT_FOUND);
		
		
		CheckLivenessFaceCompareServiceEmbedRequest checkLivenessFaceCompareServiceEmbedRequest = new CheckLivenessFaceCompareServiceEmbedRequest();
		checkLivenessFaceCompareServiceEmbedRequest.setTenantCode(request.getTenantCode());
		checkLivenessFaceCompareServiceEmbedRequest.setMsg(request.getMsg());
		
		CheckLivenessFaceCompareServiceEmbedResponse checkLivenessFaceCompareServiceEmbed = tenantEmbedLogic.checkLivenessFaceCompareServiceEmbed(checkLivenessFaceCompareServiceEmbedRequest, audit);
		
		if (checkLivenessFaceCompareServiceEmbed.getLivenessFacecompareServicesStatus().equals("0")) {
			throw new TenantException(getMessage("businesslogic.tenant.tenantlivenessfacecompareservicesnotactive", new Object[] {request.getTenantCode()}, audit), 
					ReasonTenant.TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE);
		}
		
		MsTenant tenant = embedMsgBean.getMsTenant();
		String splitLivenessFaceCompareBill = tenant.getSplitLivenessFaceCompareBill();
		
		MsLov livenessFaceCompareBalanceType = new MsLov();
		MsLov livenessFaceCompareTrxType = new MsLov();
		String livenessFaceCompareNotes = null;
		BigInteger livenessFaceCompareBalance = null;
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode("ESG");
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String refNo = trDocH.getRefNumber();
		int qty = -1;
		
		MsLov livenessBalanceType = new MsLov();
		MsLov livenessTrxType = new MsLov();
		String livenessNotes = null;
		BigInteger livenessBalance = null;
		MsLov faceCompareBalanceType = new MsLov();
		MsLov faceCompareTrxType = new MsLov();
		String faceCompareNotes = null;
		BigInteger faceCompareBalance = null;
				
		if (splitLivenessFaceCompareBill.equals("0")) {
			livenessFaceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS_FACECOMPARE);
			livenessFaceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS_FACECOMPARE);
			livenessFaceCompareNotes = "Signing Liveness Face Compare Success";
			
			livenessFaceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessFaceCompareBalanceType);
			
			if (livenessFaceCompareBalance.longValue() < 1) {
				throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessFaceCompareBalanceType.getDescription()}, this.retrieveLocaleAudit(audit)),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
			
		} else {
			livenessBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_LIVENESS);
			livenessTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_ULIVENESS);
			livenessNotes = "Signing Liveness Success";
			
			livenessBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, livenessBalanceType);
			
			faceCompareBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_FACECOMPARE);
			faceCompareTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UFACECOMPARE);
			faceCompareNotes = "Signing Face Compare Success";
			
			faceCompareBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, faceCompareBalanceType);
			
			if (livenessBalance.longValue() < 1 && faceCompareBalance.longValue() < 1) {
				throw new SaldoException(getMessage("businesslogic.saldo.balancesarenotenough", new Object[] {livenessBalanceType.getDescription(), faceCompareBalanceType.getDescription()}, audit),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (livenessBalance.longValue() < 1) {
				throw new SaldoException(getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {livenessBalanceType.getDescription()}, audit),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			} else if (faceCompareBalance.longValue() < 1) {
				throw new SaldoException(getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH, new Object[] {faceCompareBalanceType.getDescription()}, audit),
						ReasonSaldo.BALANCE_NOT_ENOUGH);
			}
		}
		
		Short livenessFacecompareRequestNum = user.getLivenessFacecompareRequestNum();
		Short livenessFacecompareValidationNum = user.getLivenessFacecompareValidationNum();
		
		Date date = new Date();
		
		if (null == user.getLivenessFacecompareRequestDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareRequestDate())) {
			livenessFacecompareRequestNum = (short) 0;
		}
		
		if (null == user.getLivenessFacecompareValidationDate() || !DateUtils.isSameDay(date, user.getLivenessFacecompareValidationDate())) {
			livenessFacecompareValidationNum = (short) 0;
		}
		
		MsTenant msTenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());

		String gsLimitRequest = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT, audit);
		short maxLimitLivenessFacecompareRequest = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT, Short.parseShort(gsLimitRequest));
		
		String gsLimitValidation = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, audit);
		short maxLimitLivenessFacecompareValidation = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT, Short.parseShort(gsLimitValidation));
		
		if ((null != livenessFacecompareRequestNum && livenessFacecompareRequestNum >= maxLimitLivenessFacecompareRequest) || (null != livenessFacecompareValidationNum && livenessFacecompareValidationNum >= maxLimitLivenessFacecompareValidation)) {
			throw new UserException(getMessage("businesslogic.user.maxlivenessfacecomparereached", null, audit),
					ReasonUser.MAX_LIVENESS_FACECOMPARE_REACHED);
		}
		
		String key = tenant.getApiKey();
		String livenessFaceCompareTenantCode = GlobalVal.TENANT_CODE_LIVENESS_FACECOMPARE + "-" + request.getTenantCode();
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND, null, audit);
		commonValidatorLogic.validateNotNull(userPersonalData.getPhotoSelf(), messageValidation, StatusCode.PHOTO_NOT_FOUND);
		
		String cutImg = MssTool.cutImageStringPrefix(request.getImg1());
		byte[] img1 =  Base64.getDecoder().decode(cutImg);
		byte[] img2 = pd.getSelfPhotoRaw();
		
		MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_LIVENESS_FACECOMPARE);
		
		LivenessFaceCompareRequest livenessFaceCompareRequest = new LivenessFaceCompareRequest(user.getLoginId(), livenessFaceCompareTenantCode, request.getVendorCode(), nik, key, img1, img2);
		LivenessFaceCompareResponse livenessFaceCompareResponse =  new LivenessFaceCompareResponse();
		
		String generalSetting = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_BYPASS_LIVENESS_FACE_COMPARE_CHECK);

		Status status = new Status();

		if ("0".equals(generalSetting) || StringUtils.isBlank(generalSetting)) {
			try {
				livenessFaceCompareResponse =  livenessFaceCompare(livenessFaceCompareRequest, audit);
			} catch(VidaException e){
				
				if (null != livenessFacecompareValidationNum) {
					Short value = (short) (livenessFacecompareValidationNum + 1);
					user.setLivenessFacecompareValidationNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareValidationNum(value);
				}
				
				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

				auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
				auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
				auditTrail.setEmail(user.getLoginId());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendorPsre);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
				
				String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
				
				byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
				
				String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
				
				auditTrail.setNotes(filename);
				daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);

				for (String docId : listDocIdEncrypt) {
					
					String documentId = commonLogic.decryptMessageToString(docId, embedMsgBean.getMsTenant().getAesEncryptKey(), audit);
					TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(user.getLoginId());
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
				}
				
				user.setLivenessFacecompareValidationDate(new Date());
				user.setUsrCrt(audit.getCallerId());
				user.setDtmUpd(date);
				daoFactory.getUserDao().updateUser(user);
				
				status = new Status();
				status.setCode(e.getErrorCode());
				status.setMessage(e.getMessage());
				response.setStatus(status);
				return  response;
			}
			
			user.setLivenessFacecompareRequestDate(date);
			status = new Status();
			status.setCode(0);	
			
			String faceCompareResult = livenessFaceCompareResponse.getResult().get(0).getFaceCompare().getCompare();
			String faceLivenessResult = livenessFaceCompareResponse.getResult().get(0).getFaceLiveness().getLive();
			String service = livenessFaceCompareResponse.getService();
			String resultStatus = "0";
			
			if(faceCompareResult.equals("True")  && faceLivenessResult.equals("True")) {
				user.setLivenessFacecompareRequestNum((short) 0);
				user.setLivenessFacecompareValidationNum((short) 0);
				user.setResetCodeRequestNum((short) 0);
				user.setUsrUpd(email);
				user.setDtmUpd(new Date());
				resultStatus = "1";
			} else {
				if (faceLivenessResult.equals(CONST_FALSE)) {
					status.setCode(StatusCode.LIVENESS_FAILED);
					status.setMessage("Verifikasi Liveness gagal. Harap mengambil Foto Selfie langsung, Pastikan wajah anda terlihat jelas tidak tertutup oleh aksesoris.");
					livenessNotes = "Signing Liveness Failed";
				} else if (faceCompareResult.equals(CONST_FALSE) && faceLivenessResult.equals("True")) {
					status.setCode(StatusCode.FACE_COMPARE_FAILED);
					status.setMessage("Verifikasi user gagal. Foto Diri tidak sesuai.");
					faceCompareNotes = "Signing Face Compare Failed";
				} else if (faceCompareResult.equals(CONST_FALSE) && faceLivenessResult.equals(CONST_FALSE)) {
					status.setCode(StatusCode.LIVENESS_AND_FACE_COMPARE_FAILED);
					status.setMessage("Verify Liveness Face Compare Gagal");
					livenessNotes = "Signing Liveness Failed";
					faceCompareNotes = "Signing Face Compare Failed";
				}
				livenessFaceCompareNotes = "Signing Liveness Face Compare Failed";
				
				if (null != livenessFacecompareRequestNum) {
					Short value = (short) (livenessFacecompareRequestNum + 1);
					user.setLivenessFacecompareRequestNum(value);
				} else {
					Short value = 1;
					user.setLivenessFacecompareRequestNum(value);
				}
			}
			
			user.setUsrCrt(audit.getCallerId());
			user.setDtmUpd(date);
			daoFactory.getUserDao().updateUser(user);
			
			if (splitLivenessFaceCompareBill.equals("0")) {
				saldoLogic.insertBalanceMutation(null, null, null, livenessFaceCompareBalanceType, livenessFaceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessFaceCompareNotes, null,trDocH.getMsOffice(),trDocH.getMsBusinessLine(), audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("1")){
				saldoLogic.insertBalanceMutation(null, null, null, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null,trDocH.getMsOffice(),trDocH.getMsBusinessLine(), audit);
			} else if (splitLivenessFaceCompareBill.equals("1") && service.equals("2")) {
				saldoLogic.insertBalanceMutation(null, null, null, livenessBalanceType, livenessTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, livenessNotes, null,trDocH.getMsOffice(),trDocH.getMsBusinessLine(), audit);
				
				nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
				
				saldoLogic.insertBalanceMutation(null, null, null, faceCompareBalanceType, faceCompareTrxType, tenant, vendor, date, refNo, qty, 
						String.valueOf(nextTrxNo), user, faceCompareNotes, null,trDocH.getMsOffice(),trDocH.getMsBusinessLine(), audit);
			}
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

			auditTrail.setPhoneNoBytea(msVendorRegisteredUser.getPhoneBytea());
			auditTrail.setHashedPhoneNo(msVendorRegisteredUser.getHashedSignerRegisteredPhone());
			auditTrail.setEmail(user.getLoginId());
			auditTrail.setAmMsUser(user);
			auditTrail.setMsTenant(tenant);
			auditTrail.setMsVendor(vendorPsre);
			auditTrail.setLovProcessType(signingProcessTypeLov);
			auditTrail.setResultStatus(resultStatus);
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(user.getLoginId());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
			
			String textFilename = String.valueOf(auditTrail.getIdSigningProcessAuditTrail()) + GlobalVal.EXTENTION_PICTURE_JPEG;
			
			byte[] zipImg1 = ZipCompressionUtils.zipBase64Data(textFilename, request.getImg1());
			
			String filename = cloudStorageLogic.storeLivenessFaceComparePhoto(auditTrail.getIdSigningProcessAuditTrail(), zipImg1);
			
			auditTrail.setNotes(filename);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);

			for (String docId : listDocIdEncrypt) {
				
				String documentId = commonLogic.decryptMessageToString(docId, embedMsgBean.getMsTenant().getAesEncryptKey(), audit);
				TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);

				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(user.getLoginId());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);
			}
		} else {
			status.setCode(0);
		}
		
		response.setStatus(status);
		return response;
	}

	private void logLivenessFaceCompareRequest(LivenessFaceCompareRequest request) {
		
		String img1Filename = GlobalVal.PREFIX_PHOTO_SELFIE_FILE_NAME + request.getLoginId() + GlobalVal.EXTENTION_PICTURE_JPEG;
		String img2Filename = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getLoginId() + GlobalVal.EXTENTION_PICTURE_JPEG;
		
		Map<String, Object> requestParam = new HashMap<>();
		requestParam.put("tenant_code", request.getTenantCode());
		requestParam.put("key", request.getKey());
		requestParam.put("nik", request.getNik());
		requestParam.put("img1", img1Filename);
		requestParam.put("img2", img2Filename);
		
		LOG.info("Liveness face compare request img1 size: {}, img2 size:{}, body: {}", request.getImg1().length, request.getImg2().length, requestParam);
	}
	
	private LivenessFaceCompareResponse livenessFaceCompare(LivenessFaceCompareRequest request, AuditContext audit) {
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
	
		mapHeader.add(headerContentType, headerMultipart);
		
		WebClient client = WebClient.create(urlVerification + urlLivenessFacecompare).headers(mapHeader);
		
		List<Attachment> atts = new LinkedList<>();
		
		ContentDisposition cdTenant = new ContentDisposition("form-data; name=\"tenant_code\"");
		ContentDisposition cdNik = new ContentDisposition("form-data; name=\"nik\"");
		ContentDisposition cdKey = new ContentDisposition("form-data; name=\"key\"");
		
		atts.add(new Attachment("tenant_code", new ByteArrayInputStream(request.getTenantCode().getBytes()), cdTenant));
		atts.add(new Attachment("nik", new ByteArrayInputStream(request.getNik().getBytes()), cdNik));
		atts.add(new Attachment("key", new ByteArrayInputStream(request.getKey().getBytes()), cdKey));
		
		try {
			String imageSelfieFileName = GlobalVal.PREFIX_PHOTO_SELFIE_FILE_NAME + request.getLoginId() ;
			ContentDisposition cdSelf = new ContentDisposition("form-data; name=\"img1\"; filename=\""+imageSelfieFileName+".jpeg\"");
			byte[] dataImageSelfiePhoto = request.getImg1();
			atts.add(new Attachment("img1", new ByteArrayInputStream(dataImageSelfiePhoto), cdSelf));
		} catch (Exception e) {
			throw new VidaException(messageSource.getMessage("businesslogic.vida.errorprocessingselfie",
					null, retrieveLocaleAudit(audit)));
			
		}
		
		try {
			String imageSelfFileName =  GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getLoginId() ;
			ContentDisposition cdDiri = new ContentDisposition("form-data; name=\"img2\"; filename=\""+imageSelfFileName+".jpeg\"");
			byte[] dataImageSelfPhoto = request.getImg2();
			atts.add(new Attachment("img2", new ByteArrayInputStream(dataImageSelfPhoto), cdDiri));
		} catch (Exception e) {
			throw new VidaException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND,
					null, retrieveLocaleAudit(audit)));
		}
		
//		try {
//			String imageKtpFileName = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + request.getLoginId();
//			ContentDisposition cdKtp = new ContentDisposition("form-data; name=\"img2\"; filename=\""+imageKtpFileName+".jpeg\"");
//			byte[] dataImageIdPhoto = request.getImg2();
//			atts.add(new Attachment("img2", new ByteArrayInputStream(dataImageIdPhoto), cdKtp));
//		} catch (Exception e) {
//			throw new VidaException(messageSource.getMessage("businesslogic.vida.errorprocessingktp",
//					null, retrieveLocaleAudit(audit)));
//		}

		logLivenessFaceCompareRequest(request);

		long startTime = System.currentTimeMillis();
		MultipartBody body = new MultipartBody(atts);
		Response response = client.post(body);
		long endTime = System.currentTimeMillis(); 
		
		long durationInMillis = endTime - startTime;


		LOG.info("LIVENESS DURATION: {}", String.valueOf(durationInMillis));
		
		LOG.info("Liveness face compare response code: {} {}", response.getStatusInfo().getStatusCode(), response.getStatusInfo().getReasonPhrase());

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Liveness face compare error", e);
		}
		
		LOG.info("Liveness face compare response: {}", result);
		
		LivenessFaceCompareResponse livenessFaceCompResponse = gson.fromJson(result, LivenessFaceCompareResponse.class);
		if (response.getStatusInfo().getStatusCode() == 500) {
			throwLivenessFailed(livenessFaceCompResponse, audit);
		}
		return livenessFaceCompResponse;
	}
	
	private void throwLivenessFailed(LivenessFaceCompareResponse livenessFaceCompResponse, AuditContext audit) {
		if (livenessFaceCompResponse.getError().equals("Face not detected")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_NOT_DETECTED, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("More than one face detected")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_MORE_THAN_ONE_FACE_DETECTED, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Face out of position")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_OUT_OF_POSITION, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Face too close")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_CLOSE, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Face too far")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_FAR, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Eye glasses detected")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_EYE_GLASSES_DETECTED, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Face too bright")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_BRIGHT, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Face too dark")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_FACE_TOO_DARK, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Masking failed")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_USER_MASKING_FAILED, null, audit));
		}
		
		if (livenessFaceCompResponse.getError().equals("Backlight detected. Retake the photo with no lights behind.")) {
			throw new VidaException(getMessage(GlobalKey.MESSAGE_ERROR_BACKLIGHT_DETECTED, null, audit));
		}
		
		throw new VidaException(livenessFaceCompResponse.getError());
	}
	
	private short getOtpActivationDefaultMaxAttempt(AuditContext audit) {
		try {
			String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_OTP_ACTIVATION_USER_DAILY, audit);
			return Short.parseShort(gsValue);
		} catch (Exception e) {
			return 3;
		}
	}
	
	private short getOtpEmbedSignMaxAttempt(MsTenant tenant, AuditContext audit) {
		// Default value SENGAJA menggunakan getOtpActivationDefaultMaxAttempt(audit) supaya sesuai setting sebelumnya
		return tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_OTP_SIGN_EMBED_MAX_ATTEMPTS, getOtpActivationDefaultMaxAttempt(audit));
	}

	@Override
	public SentOtpSigningEmbedResponse sentOtpSigningEmbed(SentOtpSigningEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		String email = embedMsgBean.getDecryptedEmail();
		
		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CODE_EMPTY, null, audit),
					ReasonUser.VENDOR_CODE_EMPTY);
		}
		
		if ( StringUtils.isBlank(request.getSendingPointOption())) {
			throw new TenantException(this.messageSource.getMessage("service.global.emptyparam",
					new String[] {CONST_SENDING_POINT_OPTION}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_API_KEY_EMPTY);
		}
		
		String sendingPointOption = request.getSendingPointOption();
		if(!GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(sendingPointOption.toUpperCase()) && !GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption.toUpperCase()) && !GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equals(sendingPointOption.toUpperCase())  ) {
				throw new UserException(getMessage("service.global.notvalid", new Object[] { CONST_SENDING_POINT_OPTION }, audit), ReasonUser.UNKNOWN);
		}
		
		AmMsuser checkUserByPhone = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), false, audit);
		String errorMessage = getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND, new Object[] { request.getPhoneNo() }, audit);
		int errorCode = StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO;
		commonValidatorLogic.validateNotNull(checkUserByPhone, errorMessage, errorCode);
		
		AmMsuser checkUserByEmail = embedMsgBean.getAmMsuser();
		if (checkUserByPhone.getIdMsUser() != checkUserByEmail.getIdMsUser()) {
			throw new UserException(getMessage("businesslogic.user.userwithphonenotregisteredwithemail", 
					new Object[] { request.getPhoneNo(), email }, audit), ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
		}
		
		AmMsuser user = checkUserByPhone;
		
		Date date = new Date();

		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());
		
		if (null == msVendorRegisteredUser) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR, null, audit),
					ReasonUser.USER_NOT_REGISTERED_IN_VENDOR);
		}

		if ("0".equals(msVendorRegisteredUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit),
					ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { request.getVendorCode() }, audit), ReasonUser.USER_CERTIFICATE_EXPIRED);
		}
		
		MsTenant msTenant = embedMsgBean.getMsTenant();
		
		TrDocumentD docDNewest = new TrDocumentD();
		List<TrDocumentD> docDs = new ArrayList<>();		

		for (String docId : request.getDocumentId()) {
			String documentIdDecrypt = commonLogic.decryptMessageToString(docId, msTenant.getAesEncryptKey(), audit);
			
			errorMessage = getMessage("businesslogic.document.documentnotfound",new Object[] {documentIdDecrypt},audit);
			errorCode = StatusCode.DOCUMENT_NOT_FOUND;

			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentIdDecrypt);
			
			commonValidatorLogic.validateNotNull(docD, errorMessage, errorCode);
			
			docDs.add(docD);
	
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), msTenant.getTenantCode());
		if (null == useroftenant) {
			throw new UserException(getMessage("businesslogic.user.usernotregisteredintenant", null, audit), 
					ReasonUser.USER_NOT_REGISTERED_IN_TENANT);
		}
		
		SendOtpValidationBean sendOtpValidationBean = new SendOtpValidationBean();
		sendOtpValidationBean.setPhoneNo(request.getPhoneNo());
		sendOtpValidationBean.setTenantCode(request.getTenantCode());
		validateSendOtpSigningConcurrent(sendOtpValidationBean, audit);

		try {
			return processSentOtpSigningEmbed(request, user, msTenant, docDs, docDNewest, msVendorRegisteredUser, date, audit);
		} finally {
			sendOtpValidationSet.remove(sendOtpValidationBean);
			LOG.info("Send OTP Signing Embed set size: {} (after cleanup)", sendOtpValidationSet.size());
		}
	}

	private SentOtpSigningEmbedResponse processSentOtpSigningEmbed(SentOtpSigningEmbedRequest request, AmMsuser user, MsTenant msTenant, List<TrDocumentD> docDs, TrDocumentD docDNewest, MsVendorRegisteredUser msVendorRegisteredUser,
			Date date, AuditContext audit) {

		// Validate OTP request and setup basic response
		OtpRequestValidationResult validationResult = validateOtpRequestAndSetup(request, user, msTenant, date, audit);

		// Determine vendor and OTP code
		String vendorCodeUpper = StringUtils.upperCase(request.getVendorCode());
		MsVendor vendor = determineVendor(vendorCodeUpper);
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);

		// Get response from validation result
		SentOtpSigningEmbedResponse response = validationResult.getResponse();

		// Send OTP based on vendor type
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendorCodeUpper)) {
			otpCode = null; // Privy handles OTP generation internally
			sendOtpPrivy(request, docDs, docDNewest, msVendorRegisteredUser, user, msTenant, vendor, audit);
		} else {
			sendOtpNonPrivy(request, otpCode, docDs, docDNewest, msVendorRegisteredUser, user, msTenant, vendor, response, audit);
		}

		// Update user with OTP code and audit information
		user.setOtpCode(otpCode);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(date);
		daoFactory.getUserDao().updateUser(user);

		return response;
	}

	/**
	 * Validates OTP request and sets up basic response configuration
	 */
	private OtpRequestValidationResult validateOtpRequestAndSetup(SentOtpSigningEmbedRequest request, AmMsuser user, MsTenant msTenant, Date date, AuditContext audit) {
		Short resetCodeRequestNum = user.getResetCodeRequestNum();

		if (null == user.getResetCodeRequestDate() || !DateUtils.isSameDay(date, user.getResetCodeRequestDate())) {
			resetCodeRequestNum = (short) 0;
		}

		user.setResetCodeRequestDate(date);

		Short maxOtpRequest = getOtpEmbedSignMaxAttempt(msTenant, audit);

		if (null != resetCodeRequestNum && resetCodeRequestNum >= maxOtpRequest) {
			throw new UserException(getMessage("businesslogic.user.maxotpsigningverificationreached", null, audit),
					ReasonUser.MAX_OTP_ACTIVATION_USER_REACHED);
		}

		if (null != resetCodeRequestNum) {
			Short value = (short) (resetCodeRequestNum + 1);
			user.setResetCodeRequestNum(value);
		} else {
			Short value = 1;
			user.setResetCodeRequestNum(value);
		}

		SentOtpSigningEmbedResponse response = new SentOtpSigningEmbedResponse();

		String durationResendOtp = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_DURATION_RESEND_OTP);
		int resendOtpDuration = tenantSettingsLogic.getSettingValue(msTenant, GlobalVal.CODE_LOV_TENANT_SETTING_DURATION_DURATION_RESEND_OTP_SIGNING, Integer.parseInt(durationResendOtp));

		response.setDurationResendOTP(resendOtpDuration);

		return new OtpRequestValidationResult(response, resendOtpDuration);
	}

	private MsVendor determineVendor(String vendorCodeUpper) {
		if (vendorCodeUpper.equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
			return daoFactory.getVendorDao().getVendorByCode(vendorCodeUpper);
		} else {
			return daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		}
	}

	private SigningProcessAuditTrailBean createAuditTrailBean(List<TrDocumentD> docDs, MsVendorRegisteredUser msVendorRegisteredUser,
			String phoneNo, MsTenant msTenant, AmMsuser user, MsVendor vendor, String otpCode) {

		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EMBED_V2);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP);

		SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
		auditTrailBean.setDocumentDs(docDs);
		auditTrailBean.setEmail(msVendorRegisteredUser.getSignerRegisteredEmail());
		auditTrailBean.setLovProcessType(signingProcessTypeLov);
		auditTrailBean.setLovSendingPoint(sendingPointLov);
		auditTrailBean.setNotes("Request OTP Signing");
		auditTrailBean.setPhone(phoneNo);
		auditTrailBean.setTenant(msTenant);
		auditTrailBean.setUser(user);
		auditTrailBean.setVendorPsre(vendor);

		if (otpCode != null) {
			auditTrailBean.setOtpCode(otpCode);
		}

		return auditTrailBean;
	}

	private void sendOtpPrivy(SentOtpSigningEmbedRequest request, List<TrDocumentD> docDs, TrDocumentD docDNewest,
			MsVendorRegisteredUser msVendorRegisteredUser, AmMsuser user, MsTenant msTenant, MsVendor vendor, AuditContext audit) {

		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msTenant, vendor, audit);

		SigningProcessAuditTrailBean auditTrailBean = createAuditTrailBean(docDs, msVendorRegisteredUser, request.getPhoneNo(), msTenant, user, vendor, null);

		if (request.getDocumentId() == null) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
					ReasonDocument.EMPTY_DOCUMENT_ID);
		}

		this.sendOtpPrivyGeneral(request.getPhoneNo(), msVendorRegisteredUser, user, msTenant, vendor, request.getDocumentId(),
				docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(), auditTrailBean, audit);
	}

	private static class GatewayConfiguration {
		private final String smsGateway;
		private final String waGateway;

		public GatewayConfiguration(String smsGateway, String waGateway) {
			this.smsGateway = smsGateway;
			this.waGateway = waGateway;
		}

		public String getSmsGateway() { return smsGateway; }
		public String getWaGateway() { return waGateway; }
	}

	private GatewayConfiguration determineGatewayConfiguration(MsTenant msTenant) {
		MsNotificationtypeoftenant notificationType = daoFactory.getNotificationtypeoftenantDao()
				.getNotificationType(msTenant, NotificationSendingPoint.OTP_SIGN_EMBED_V2.toString());

		String smsGateway;
		String waGateway;

		if (notificationType != null) {
			smsGateway = notificationType.getLovSmsGateway() == null ?
					GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST : notificationType.getLovSmsGateway().getCode();
			waGateway = notificationType.getLovWaGateway() == null ?
					GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS : notificationType.getLovWaGateway().getCode();
		} else {
			smsGateway = msTenant.getLovSmsGateway() == null ?
					GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST : msTenant.getLovSmsGateway().getCode();
			waGateway = GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS;
		}

		return new GatewayConfiguration(smsGateway, waGateway);
	}

	private void sendOtpByEmail(String otpCode, MsTenant msTenant, AmMsuser user, MsVendorRegisteredUser msVendorRegisteredUser,
			SigningProcessAuditTrailBean auditTrailBean, SentOtpSigningEmbedResponse response, AuditContext audit) {

		if (msVendorRegisteredUser.getEmailService().equals("1")) {
			throw new UserException(getMessage("businesslogic.user.userregisterwithoutemail", null, audit),
					ReasonUser.USER_REGISTERED_WITHOUT_EMAIL);
		}

		Map<String, Object> templateParameters = new HashMap<>();
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("fullname", user.getFullName());
		userMap.put("otp", otpCode);

		MsMsgTemplate template = null;
		if (msTenant.getOtpActiveDuration() != null && msTenant.getOtpActiveDuration() > 0) {
			userMap.put(GlobalVal.DURATION, msTenant.getOtpActiveDuration());
			templateParameters.put("user", userMap);
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_VERIF_EMAIL_WITH_DURATION, templateParameters);
		} else {
			templateParameters.put("user", userMap);
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP, templateParameters);
		}

		response.setOtpByEmail("1");
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(new String[] { msVendorRegisteredUser.getSignerRegisteredEmail() });
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());

		try {
			emailSenderLogic.sendEmail(emailInfo, null, auditTrailBean);
			LOG.info("Send OTP EMAIL : {}", msVendorRegisteredUser.getSignerRegisteredEmail());
		} catch (MessagingException e) {
			throw new EmailException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
					this.retrieveLocaleAudit(audit)), ReasonEmail.SEND_EMAIL_ERROR);
		}
	}

	private void sendOtpNonPrivy(SentOtpSigningEmbedRequest request, String otpCode, List<TrDocumentD> docDs,
			TrDocumentD docDNewest, MsVendorRegisteredUser msVendorRegisteredUser, AmMsuser user, MsTenant msTenant,
			MsVendor vendor, SentOtpSigningEmbedResponse response, AuditContext audit) {

		GatewayConfiguration gatewayConfig = determineGatewayConfiguration(msTenant);
		MsVendor vendorPsre = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		SigningProcessAuditTrailBean auditTrailBean = createAuditTrailBean(docDs, msVendorRegisteredUser, request.getPhoneNo(), msTenant, user, vendorPsre, otpCode);

		String sendingPointOption = request.getSendingPointOption();

		if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption)) {
			sendOtpByWhatsApp(request.getPhoneNo(), otpCode, msTenant, user, docDNewest, vendor, gatewayConfig, auditTrailBean, response, audit);
		} else if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL.equals(sendingPointOption)) {
			sendOtpByEmail(otpCode, msTenant, user, msVendorRegisteredUser, auditTrailBean, response, audit);
		} else {
			sendOtpBySms(request.getPhoneNo(), otpCode, msTenant, user, docDNewest, vendor, gatewayConfig, auditTrailBean, response, audit);
		}
	}

	private void sendOtpByWhatsApp(String phoneNo, String otpCode, MsTenant msTenant, AmMsuser user, TrDocumentD docDNewest,
			MsVendor vendor, GatewayConfiguration gatewayConfig, SigningProcessAuditTrailBean auditTrailBean,
			SentOtpSigningEmbedResponse response, AuditContext audit) {

		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP, msTenant, vendor, audit);

		if (GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP.equals(gatewayConfig.getWaGateway())) {
			this.sendOtpWa(phoneNo, otpCode, msTenant, user, docDNewest.getTrDocumentH(),
					docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(),
					docDNewest.getTrDocumentH().getRefNumber(), auditTrailBean, audit);
			LOG.info("Send OTP WA : {}", phoneNo);
		} else {
			this.sendOtpWaHaloSis(phoneNo, otpCode, msTenant, user, docDNewest.getTrDocumentH(),
					docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(),
					docDNewest.getTrDocumentH().getRefNumber(), auditTrailBean, audit);
			LOG.info("Send OTP WA Halosis : {}", phoneNo);
		}
		response.setOtpByEmail("0");
	}

	private void sendOtpBySms(String phoneNo, String otpCode, MsTenant msTenant, AmMsuser user, TrDocumentD docDNewest,
			MsVendor vendor, GatewayConfiguration gatewayConfig, SigningProcessAuditTrailBean auditTrailBean,
			SentOtpSigningEmbedResponse response, AuditContext audit) {

		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msTenant, vendor, audit);

		if (gatewayConfig.getSmsGateway().equals(GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS)) {
			this.sendOtpSmsJatis(phoneNo, otpCode, msTenant, user, docDNewest.getTrDocumentH(),
					docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(),
					docDNewest.getTrDocumentH().getRefNumber(), auditTrailBean, audit);
		} else {
			this.sendOtpSms(phoneNo, otpCode, msTenant, user, docDNewest.getTrDocumentH(),
					docDNewest.getTrDocumentH().getMsOffice(), docDNewest.getTrDocumentH().getMsBusinessLine(),
					docDNewest.getTrDocumentH().getRefNumber(), auditTrailBean, audit);
			LOG.info("Send OTP SMS VFIRST: {}", phoneNo);
		}
		response.setOtpByEmail("0");
	}

	// Inner class to hold validation result
	private static class OtpRequestValidationResult {
		private final SentOtpSigningEmbedResponse response;
		private final int resendOtpDuration;

		public OtpRequestValidationResult(SentOtpSigningEmbedResponse response, int resendOtpDuration) {
			this.response = response;
			this.resendOtpDuration = resendOtpDuration;
		}

		public SentOtpSigningEmbedResponse getResponse() { return response; }
		public int getResendOtpDuration() { return resendOtpDuration; }
	}

	private void sendOtpSms(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String refNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);
		

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		MsMsgTemplate template = null;
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0){
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		}else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		
		SendSmsResponse responseSms = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, template.getBody(), tenant);
		if (gs.getGsValue().equals("1")) {
			responseSms = smsOtpLogic.sendSms(sendSmsValueFirstRequestBean, auditTrailBean);
		} else {
			LOG.info("Send SMS OTP Success");
			responseSms.setTrxNo(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		}

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;

		if (responseSms.getErrorCode() == null || 
				(!responseSms.getErrorCode().equals(VFIRST_ERR28682)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR28681)
				&& !responseSms.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					-1, String.valueOf(responseSms.getTrxNo()), user, notes, responseSms.getGuid(), office, businessLine, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, docH, null, balanceType, trxType, tenant, vendor, new Date(), refNo,
					0, String.valueOf(responseSms.getTrxNo()), user, notes + " error " + responseSms.getErrorCode(),
					responseSms.getGuid(), office, businessLine, audit);
		}
	}
	
	private void sendOtpWa(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String refNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		String templateCode = "";
		if ( tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			templateCode = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration().toString();
		}
		else {
			templateCode = GlobalVal.TEMPLATE_OTP_WA;
		}

		String notes = phoneNo + GlobalVal.SEND_OTP_WA_SIGNING;
			
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateCode);
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;

		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(template);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(buttonText);
		request.setMsTenant(tenant);
		request.setPhoneNumber(phoneNo);
		request.setMsOffice(office);
		request.setMsBusinessLine(businessLine);
		request.setRefNo(refNo);
		request.setNotes(notes);
		request.setAmMsuser(user);
		request.setIsOtp(true);
		request.setTrDocumentH(docH);
		boolean removeHeader = true;
		request.setRemoveHeader(removeHeader);
		
		
		if (gs.getGsValue().equals("1")) {
			whatsAppLogic.sendMessage(request,auditTrailBean, audit);
		} else {
			LOG.info("Send WA OTP Success");
		}

	}
	
	private void sendOtpWaHaloSis(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String refNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		
		MsMsgTemplate templateWa = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			String templateWaActiveDuration = GlobalVal.TEMPLATE_OTP_WA_WITH_DURATION + "_" + tenant.getOtpActiveDuration();
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateWaActiveDuration);
			if (templateWa == null) {
				throw new TenantException(getMessage("businesslogic.tenant.msgtemplatewithotpactiveduration", new Object[] { tenant.getOtpActiveDuration() }, audit), 
						ReasonTenant.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND);
			}
		} else {
			templateWa = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, GlobalVal.TEMPLATE_OTP_WA);
		}
		
		String notes = phoneNo + GlobalVal.SEND_OTP_WA_SIGNING;

		HalosisSendWhatsAppRequestBean sendWhatsappHalosisRequest = new HalosisSendWhatsAppRequestBean();
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(otpCode);
		String buttonText = otpCode;
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_OTP_USER);
		if (gs.getGsValue().equals("1")) {
			sendWhatsappHalosisRequest.setTemplate(templateWa);
			sendWhatsappHalosisRequest.setBodyTexts(bodyTexts);
			sendWhatsappHalosisRequest.setButtonText(buttonText);
			sendWhatsappHalosisRequest.setReservedTrxNo(trxNo);
			sendWhatsappHalosisRequest.setPhoneNumber(phoneNo);
			sendWhatsappHalosisRequest.setAmMsuser(user);
			sendWhatsappHalosisRequest.setMsTenant(tenant);
			sendWhatsappHalosisRequest.setMsOffice(office);
			sendWhatsappHalosisRequest.setMsBusinessLine(businessLine);
			sendWhatsappHalosisRequest.setRefNo(refNo);
			sendWhatsappHalosisRequest.setNotes(notes);
			sendWhatsappHalosisRequest.setIsOtp(true);
			sendWhatsappHalosisRequest.setTrDocumentH(docH);
			whatsAppHalosisLogic.sendMessage(sendWhatsappHalosisRequest, auditTrailBean, audit);
		}
		else {
			LOG.info("Send WA OTP HALOSIS  Signing success");
		}
	}

	@Transactional(noRollbackFor={PrivyException.class})
	@Override
	public VerifyOtpSigningEmbedResponse verifyOtpSigningEmbed(VerifyOtpSigningEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 embedMsgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		String email = embedMsgBean.getDecryptedEmail();
		String validationMessage = "";
		
		boolean checkUserExistence = false;
		
		AmMsuser checkUserByPhone = userValidatorLogic.validateGetUserByPhone(request.getPhoneNo(), checkUserExistence, audit);
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND, new Object[] {request.getPhoneNo() }, audit);
		commonValidatorLogic.validateNotNull(checkUserByPhone, validationMessage, StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		
		AmMsuser checkUserByEmail = userValidatorLogic.validateGetUserByEmailv2(email, checkUserExistence, audit);		
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, new Object[] {email }, audit);
		commonValidatorLogic.validateNotNull(checkUserByEmail, validationMessage, StatusCode.USER_NOT_FOUND_WITH_THAT_EMAIL);
		
		List<String> listDocument = request.getDocumentId();
		if (request.getDocumentId() == null) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
					ReasonDocument.EMPTY_DOCUMENT_ID);
		}
		
		if (checkUserByPhone.getIdMsUser() != checkUserByEmail.getIdMsUser()) {
			throw new UserException(getMessage("businesslogic.user.userwithphonenotregisteredwithemail", 
					new Object[] { request.getPhoneNo(), email }, audit), ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
		}
		
		AmMsuser user = checkUserByPhone;
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		
		if (!request.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID) && tenant.getOtpActiveDuration() != null &&tenant.getOtpActiveDuration() > 0 && user.getOtpCode() != null && user.getResetCodeRequestDate() != null) {
			
				Date nowDate = new Date();
				Date requestDate = user.getResetCodeRequestDate();
				long milliSecondsDiff = nowDate.getTime() - requestDate.getTime();
				double minutesDifff =  (double)  milliSecondsDiff / DateUtils.MILLIS_PER_MINUTE;
				
				LOG.info("tenant otp Active Duration : {} | verify OTP activation duration : {}",tenant.getOtpActiveDuration(),minutesDifff);
				if (minutesDifff > (double) tenant.getOtpActiveDuration()) {
					user.setUsrUpd("SYSTEM");
					user.setDtmUpd(new Date());
					daoFactory.getUserDao().updateUser(user);
					
					Status stts = new Status();
					stts.setCode(StatusCode.OTP_EXPIRED);
					stts.setMessage(getMessage(GlobalKey.MESSAGE_ERROR_USER_EXPIRED_OTP_CODE,
							null, audit));
					VerifyOtpSigningEmbedResponse response = new VerifyOtpSigningEmbedResponse();
					response.setStatus(stts);
					return response;
				}
				
		}
		
		MsLov sendingPointLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EMBED_V2);
		MsLov signingProcessTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP);

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());

		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), request.getVendorCode());

		if (request.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID))	{
			
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, new Object[] {"Vendor" }, audit);
			commonValidatorLogic.validateNotNull(vendor, validationMessage, StatusCode.VENDOR_NOT_FOUND);
			
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit);

			commonValidatorLogic.validateNotNull(request.getDocumentId(), validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
			
			TrPsreSigningConfirmation tpsc = daoFactory.getDocumentDao().getTrPsreSigningConfirmationByIdMsUser(user.getIdMsUser());
			validationMessage = getMessage("businesslogic.user.requestotp", null, audit);
			commonValidatorLogic.validateNotNull(tpsc, validationMessage, StatusCode.OTP_INVALID);
			
			List<TrDocumentD> docD = new ArrayList<>();
			
			for (String documentId : request.getDocumentId()) {
				String documentIdDecrypt = commonLogic.decryptMessageToString(documentId, tenant.getAesEncryptKey(), audit);

				TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentIdDecrypt);
				
				docD.add(documentD);
			}
			
			SigningProcessAuditTrailBean auditTrailBean = new SigningProcessAuditTrailBean();
			
			auditTrailBean.setPhone(request.getPhoneNo());
			auditTrailBean.setUser(user);
			auditTrailBean.setTenant(tenant);
			auditTrailBean.setVendorPsre(vendor);
			auditTrailBean.setDocumentDs(docD);
			auditTrailBean.setEmail(vendorUser.getSignerRegisteredEmail());
			
			
			PrivyGeneralOtpValidationResponse privyConfirmOtp = privyGeneralLogic.otpValidation(request.getOtpCode(), tpsc, tenant, vendor, auditTrailBean, audit);
			
			if (privyConfirmOtp.getError() != null) {
				String errorMessage = privyGeneralLogic.buildOtpValidationErrorMessage(privyConfirmOtp, audit);

				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
				auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtpCode());
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

				throw new PrivyException(errorMessage);
			}
			daoFactory.getDocumentDao().deletePsreSigningConfirmation(tpsc);
			
		}
		else {
			if (!request.getOtpCode().equals(user.getOtpCode())) {

				TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
				auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
				auditTrail.setAmMsUser(user);
				auditTrail.setMsTenant(tenant);
				auditTrail.setMsVendor(vendor);
				auditTrail.setLovSendingPoint(sendingPointLov);
				auditTrail.setLovProcessType(signingProcessTypeLov);
				auditTrail.setOtpCode(request.getOtpCode());
				auditTrail.setResultStatus("0");
				auditTrail.setDtmCrt(new Date());
				auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);

				throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_WRONG_OTP_CODE, null,
						this.retrieveLocaleAudit(audit)), ReasonUser.OTP_INVALID);
			}
		}
		
		
		Short resetCodeRequestNum = 0;

		user.setOtpCode(null);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(new Date());
		user.setResetCodeRequestNum(resetCodeRequestNum);
		user.setLivenessFacecompareRequestNum(resetCodeRequestNum);
		user.setLivenessFacecompareValidationNum(resetCodeRequestNum);
		daoFactory.getUserDao().updateUser(user);

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();

		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(request.getPhoneNo()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(request.getPhoneNo()));
		auditTrail.setEmail(vendorUser.getSignerRegisteredEmail());
		auditTrail.setAmMsUser(user);
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setLovSendingPoint(sendingPointLov);
		auditTrail.setLovProcessType(signingProcessTypeLov);
		auditTrail.setOtpCode(request.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(vendorUser.getSignerRegisteredEmail());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		return new VerifyOtpSigningEmbedResponse();
	}
	
	private void sendOtpSmsJatis(String phoneNo, String otpCode, MsTenant tenant, AmMsuser user, TrDocumentH docH, MsOffice office, MsBusinessLine businessLine, String refNo, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		Map<String, Object> param = new HashMap<>();
		param.put("otp", otpCode);

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_OTP_USER);
		
		MsMsgTemplate template = null;
		
		if (tenant.getOtpActiveDuration() != null && tenant.getOtpActiveDuration() > 0) {
			param.put(GlobalVal.DURATION, tenant.getOtpActiveDuration());
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_OTP_SMS_WITH_DURATION, param);
		} else {
			template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_KODE_OTP_SMS, param);
		}
		
		if (gs.getGsValue().equals("1")) {
			long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;
			JatisSmsRequestBean request = new JatisSmsRequestBean(tenant,office, businessLine, phoneNo, template.getBody(), String.valueOf(trxNo), refNo, true);
			jatisSmsLogic.sendSmsAndCutBalance(request, docH, null, user, notes, audit, auditTrailBean);
		} else {
			LOG.info("Send SMS OTP Jatis Success");
		}		
	}
	
	private void sendOtpPrivyGeneral(String phoneNo, MsVendorRegisteredUser msVendorRegisteredUser, AmMsuser user, MsTenant tenant, MsVendor vendor, List<String> documentId, MsOffice office, MsBusinessLine businessLine, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		if (StringUtils.isBlank(msVendorRegisteredUser.getVendorRegistrationId())) {
			throw new PrivyException(getMessage(GlobalKey.MESSAGE_ERROR_PRIVY_IDNOTFOUND, null, audit));
		}
		
		String trxNo =  String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		TrDocumentD docDNewest = new TrDocumentD();
		
		List<String> documentIdsOtpRequestPrivy = new ArrayList<>();
		for (int i = 0; i < documentId.size(); i++ ) {
			String documentIdDecrypt = commonLogic.decryptMessageToString(documentId.get(i), tenant.getAesEncryptKey(), audit);
			
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentIdDecrypt);
			if (null == docDNewest.getRequestDate() || docD.getRequestDate().after(docDNewest.getRequestDate())  ) {
				docDNewest = docD;
			}
			
			String documentIdAlphanumeric = documentIdDecrypt.replace("-", "");
			documentIdsOtpRequestPrivy.add(documentIdAlphanumeric);
		}
		
		String documentIds = String.join(", ", documentIdsOtpRequestPrivy);
		LOG.info("documentIds : {}", documentIds);
		
		TrPsreSigningConfirmation psreSigningConfirmation = new TrPsreSigningConfirmation(user, documentIds, null);
		psreSigningConfirmation.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		psreSigningConfirmation.setDtmCrt(new Date());
		
		daoFactory.getDocumentDao().insertPsreSigningConfirmation(psreSigningConfirmation);
		
		PrivyGeneralOtpRequestSigningResponse otpRequestSigning = privyGeneralLogic.otpRequestSigning(msVendorRegisteredUser.getVendorRegistrationId(), tenant, vendor, documentIdsOtpRequestPrivy, audit);
		if (otpRequestSigning.getError() != null) {
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			if (auditTrailBean.getDocumentDs() != null ) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
				}
			}
			
			String errorMessage = privyGeneralLogic.buildOtpRequestSigningErrorMessage(otpRequestSigning, audit);
			throw new PrivyException(errorMessage);
		}
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(auditTrailBean.getEmail());
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
		auditTrail.setNotificationVendor(GlobalVal.VENDOR_CODE_PRIVY_ID);
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		if (auditTrailBean.getDocumentDs() != null ) {
			for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);;
			}
		}
		
		psreSigningConfirmation.setTransactionId(otpRequestSigning.getData().getTransactionId());
		daoFactory.getDocumentDao().updatePsreSigningConfirmation(psreSigningConfirmation);
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
		
		String notes = phoneNo + GlobalVal.SEND_OTP_SMS_SIGNING;
		
		saldoLogic.insertBalanceMutation(null, docDNewest.getTrDocumentH(), null, balanceType, trxType, tenant, vendor, new Date(), docDNewest.getTrDocumentH().getRefNumber(),
				-1, trxNo, user, notes, null,office, businessLine, audit);
	}

	private void validateSendOtpSigningConcurrent(SendOtpValidationBean bean, AuditContext audit) {
		LOG.info("Checking send otp phone: {}, tenant: {}", bean.getPhoneNo(), bean.getTenantCode());
		LOG.info("Send OTP Signing set size: {} (before validation)", sendOtpValidationSet.size());
		if (!sendOtpValidationSet.add(bean)) {
			throw new CommonException(getMessage("businesslogic.global.otpalreadysend", null, audit), ReasonCommon.ALREADY_PROCESSED);
		}
	}
}
